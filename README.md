# Ultimate Startup Co-founder Backend

Real AI-powered backend with CrewAI, LangChain, LiveKit, and Composio integrations.

## Features

- **CrewAI Integration**: Real multi-agent AI system with specialized co-founder agents
- **<PERSON><PERSON><PERSON>n**: Advanced language model orchestration and prompt engineering
- **LiveKit**: Real-time video/voice communication with AI agents
- **Composio**: 200+ third-party integrations (Slack, GitHub, Notion, etc.)
- **FastAPI**: High-performance async API with automatic documentation
- **WebSocket Support**: Real-time updates and communication
- **SQLAlchemy**: Database ORM with migration support

## Quick Start

### 1. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. Environment Setup

```bash
cp .env.example .env
# Edit .env with your API keys
```

Required API keys:
- **OpenAI API Key**: For CrewAI and LangChain
- **LiveKit Credentials**: For video/voice sessions
- **Composio API Key**: For third-party integrations

### 3. Run the Server

```bash
python run.py
```

The server will start at `http://localhost:8000`

- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## API Endpoints

### Agents
- `GET /api/v1/agents` - List all AI agents
- `POST /api/v1/agents/execute` - Execute single agent task
- `POST /api/v1/agents/execute-multi` - Execute multi-agent task

### Orchestrator
- `POST /api/v1/orchestrator/process` - Process user input with full orchestration
- `POST /api/v1/orchestrator/execute-suggestion` - Execute suggested actions

### LiveKit
- `POST /api/v1/livekit/sessions` - Create video/voice session
- `GET /api/v1/livekit/sessions/{id}` - Get session details
- `POST /api/v1/livekit/sessions/{id}/start` - Start session
- `POST /api/v1/livekit/sessions/{id}/end` - End session

### Integrations
- `GET /api/v1/integrations` - List available integrations
- `POST /api/v1/integrations/{id}/connect` - Connect integration
- `POST /api/v1/integrations/{id}/execute` - Execute integration action

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   AI Services   │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (CrewAI)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Integrations  │
                       │   (Composio)    │
                       └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   LiveKit       │
                       │   (Video/Voice) │
                       └─────────────────┘
```

## AI Agents

### Strategic Co-founder (Alex)
- Market analysis and competitive research
- TAM/SAM/SOM calculations
- Strategic planning and business modeling

### Product Co-founder (Sam)
- User research and validation
- Product strategy and roadmapping
- Feature prioritization (RICE framework)

### Technical Co-founder (Taylor)
- System architecture design
- Technology stack recommendations
- Code generation and review

### Operations Co-founder (Jordan)
- Legal compliance and business structure
- Financial modeling and projections
- Process optimization and automation

### Marketing Co-founder (Morgan)
- Brand positioning and messaging
- Growth strategy and customer acquisition
- Content marketing and campaigns

## Development

### Project Structure

```
backend/
├── app/
│   ├── api/routes/          # API route handlers
│   ├── core/               # Core configuration
│   ├── models/             # Database models
│   └── services/           # Business logic services
├── requirements.txt        # Python dependencies
├── main.py                # FastAPI application
└── run.py                 # Development server
```

### Adding New Agents

1. Update `crewai_service.py` with new agent configuration
2. Add agent to the agents list in `routes/agents.py`
3. Update frontend to include new agent

### Adding New Integrations

1. Add integration config to `composio_service.py`
2. Implement action handlers for the integration
3. Update frontend integration panel

## Deployment

### Docker (Recommended)

```bash
# Build image
docker build -t ultimate-cofounder-backend .

# Run container
docker run -p 8000:8000 --env-file .env ultimate-cofounder-backend
```

### Production

For production deployment:

1. Set `RELOAD=false` in environment
2. Use a production WSGI server like Gunicorn
3. Set up proper logging and monitoring
4. Configure database connection pooling
5. Set up Redis for session management

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `OPENAI_API_KEY` | OpenAI API key for AI agents | Yes |
| `LIVEKIT_API_KEY` | LiveKit API key | Yes |
| `LIVEKIT_API_SECRET` | LiveKit API secret | Yes |
| `LIVEKIT_URL` | LiveKit server URL | Yes |
| `COMPOSIO_API_KEY` | Composio API key | Yes |
| `DATABASE_URL` | Database connection string | No |
| `REDIS_URL` | Redis connection string | No |
| `SECRET_KEY` | JWT secret key | Yes |

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see LICENSE file for details.