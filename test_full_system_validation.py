#!/usr/bin/env python3
"""
Full System Validation Test Suite
Comprehensive testing of all Ultimate Co-founder functionality including TopView.ai integration
"""

import asyncio
import json
import sys
import time
from typing import Dict, Any
import httpx
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER = {
    "username": "<EMAIL>",
    "password": "demo123"
}

class FullSystemValidator:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.auth_token = None
        self.test_results = []
        
    async def authenticate(self) -> bool:
        """Authenticate and get JWT token"""
        try:
            response = await self.client.post(
                f"{BASE_URL}/api/v1/auth/login",
                data=TEST_USER,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                print(f"✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authorization headers"""
        return {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json"
        }
    
    async def test_health_endpoints(self) -> bool:
        """Test all health endpoints"""
        try:
            # Main health endpoint
            response = await self.client.get(f"{BASE_URL}/health")
            if response.status_code != 200:
                print(f"❌ Main health endpoint failed: {response.status_code}")
                return False
            
            # API health endpoint (with trailing slash)
            response = await self.client.get(f"{BASE_URL}/api/v1/health/")
            if response.status_code != 200:
                print(f"❌ API health endpoint failed: {response.status_code}")
                return False
            
            print("✅ All health endpoints working")
            return True
            
        except Exception as e:
            print(f"❌ Health endpoints error: {e}")
            return False
    
    async def test_agents_functionality(self) -> bool:
        """Test AI agents functionality"""
        try:
            # List agents
            response = await self.client.get(
                f"{BASE_URL}/api/v1/agents/",
                headers=self.get_auth_headers()
            )
            if response.status_code != 200:
                print(f"❌ List agents failed: {response.status_code}")
                return False
            
            # Execute single agent
            response = await self.client.post(
                f"{BASE_URL}/api/v1/agents/execute?agent_id=strategic",
                json={"description": "Test strategic analysis"},
                headers=self.get_auth_headers()
            )
            if response.status_code != 200:
                print(f"❌ Single agent execution failed: {response.status_code}")
                return False
            
            # Execute multi-agent
            response = await self.client.post(
                f"{BASE_URL}/api/v1/agents/execute-multi",
                json={
                    "description": "Test multi-agent collaboration",
                    "agent_ids": ["strategic", "product"]
                },
                headers=self.get_auth_headers()
            )
            if response.status_code != 200:
                print(f"❌ Multi-agent execution failed: {response.status_code}")
                return False
            
            print("✅ AI agents functionality working")
            return True
            
        except Exception as e:
            print(f"❌ Agents functionality error: {e}")
            return False
    
    async def test_skywork_integration(self) -> bool:
        """Test SkyworkAI integration"""
        try:
            response = await self.client.post(
                f"{BASE_URL}/api/v1/skywork/generate",
                json={
                    "type": "business_plan",
                    "title": "Test Business Plan",
                    "prompt": "Create a test business plan"
                },
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                print("✅ SkyworkAI integration working")
                return True
            else:
                print(f"❌ SkyworkAI integration failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ SkyworkAI integration error: {e}")
            return False
    
    async def test_livekit_integration(self) -> bool:
        """Test LiveKit integration"""
        try:
            response = await self.client.post(
                f"{BASE_URL}/api/v1/livekit/sessions",
                json={
                    "agent_ids": ["strategic", "product"],
                    "session_type": "video"
                },
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                print("✅ LiveKit integration working")
                return True
            else:
                print(f"❌ LiveKit integration failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ LiveKit integration error: {e}")
            return False
    
    async def test_topview_integration(self) -> bool:
        """Test TopView.ai integration"""
        try:
            # Health check
            response = await self.client.get(
                f"{BASE_URL}/api/v1/topview/health",
                headers=self.get_auth_headers()
            )
            if response.status_code != 200:
                print(f"❌ TopView.ai health failed: {response.status_code}")
                return False
            
            # URL to Video
            response = await self.client.post(
                f"{BASE_URL}/api/v1/topview/url-to-video",
                json={
                    "url": "https://example.com",
                    "duration": 30,
                    "avatarId": "default"
                },
                headers=self.get_auth_headers()
            )
            if response.status_code != 200:
                print(f"❌ TopView.ai URL to Video failed: {response.status_code}")
                return False
            
            # Video Avatar
            response = await self.client.post(
                f"{BASE_URL}/api/v1/topview/video-avatar",
                json={
                    "script": "Test video avatar script",
                    "avatarId": "default"
                },
                headers=self.get_auth_headers()
            )
            if response.status_code != 200:
                print(f"❌ TopView.ai Video Avatar failed: {response.status_code}")
                return False
            
            print("✅ TopView.ai integration working")
            return True
            
        except Exception as e:
            print(f"❌ TopView.ai integration error: {e}")
            return False
    
    async def test_authentication_flow(self) -> bool:
        """Test complete authentication flow"""
        try:
            # Test /me endpoint
            response = await self.client.get(
                f"{BASE_URL}/api/v1/auth/me",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                user_data = response.json()
                print(f"✅ Authentication flow working - User: {user_data.get('email', 'Unknown')}")
                return True
            else:
                print(f"❌ Authentication flow failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication flow error: {e}")
            return False
    
    async def test_api_documentation(self) -> bool:
        """Test API documentation endpoints"""
        try:
            # OpenAPI docs
            response = await self.client.get(f"{BASE_URL}/docs")
            if response.status_code != 200:
                print(f"❌ API docs failed: {response.status_code}")
                return False
            
            # OpenAPI JSON
            response = await self.client.get(f"{BASE_URL}/openapi.json")
            if response.status_code != 200:
                print(f"❌ OpenAPI JSON failed: {response.status_code}")
                return False
            
            print("✅ API documentation accessible")
            return True
            
        except Exception as e:
            print(f"❌ API documentation error: {e}")
            return False
    
    async def run_full_validation(self):
        """Run complete system validation"""
        print("🚀 Starting Full System Validation")
        print("=" * 60)
        
        # Authentication
        if not await self.authenticate():
            print("❌ Cannot proceed without authentication")
            return False
        
        # Test suite
        tests = [
            ("Health Endpoints", self.test_health_endpoints),
            ("Authentication Flow", self.test_authentication_flow),
            ("AI Agents Functionality", self.test_agents_functionality),
            ("SkyworkAI Integration", self.test_skywork_integration),
            ("LiveKit Integration", self.test_livekit_integration),
            ("TopView.ai Integration", self.test_topview_integration),
            ("API Documentation", self.test_api_documentation),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 Testing: {test_name}")
            try:
                if await test_func():
                    passed += 1
                    self.test_results.append({"test": test_name, "status": "PASS"})
                else:
                    self.test_results.append({"test": test_name, "status": "FAIL"})
            except Exception as e:
                print(f"❌ {test_name} crashed: {e}")
                self.test_results.append({"test": test_name, "status": "ERROR", "error": str(e)})
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 FULL SYSTEM VALIDATION SUMMARY")
        print("=" * 60)
        
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"{status_icon} {result['test']}: {result['status']}")
            if "error" in result:
                print(f"   Error: {result['error']}")
        
        print(f"\n📈 Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
        
        if passed == total:
            print("🎉 FULL SYSTEM VALIDATION PASSED!")
            print("🚀 Ultimate Co-founder is PRODUCTION READY!")
            return True
        else:
            print("⚠️  Some system validation tests FAILED!")
            return False
    
    async def close(self):
        """Clean up resources"""
        await self.client.aclose()

async def main():
    """Main validation runner"""
    validator = FullSystemValidator()
    try:
        success = await validator.run_full_validation()
        return 0 if success else 1
    finally:
        await validator.close()

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
