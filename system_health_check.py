#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive System Health Check for Ultimate Co-founder Platform
Tests all critical components: authentication, database, AI services, integrations
"""

import asyncio
import json
import sys
import time
import os
from typing import Dict, Any, List
import httpx
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Test configuration
BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:5173"
TEST_USER = {
    "username": "<EMAIL>",
    "password": "demo123"
}

class SystemHealthChecker:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.auth_token = None
        self.test_results = []
        self.start_time = datetime.now()
        
    async def check_backend_health(self) -> bool:
        """Check if backend server is running and healthy"""
        try:
            print("Checking backend health...")
            response = await self.client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("Backend health check: PASS")
                return True
            else:
                print(f"Backend health check: FAIL (Status: {response.status_code})")
                return False
        except Exception as e:
            print(f"Backend health check: FAIL (Error: {e})")
            return False
    
    async def check_database_connectivity(self) -> bool:
        """Check database connectivity"""
        try:
            print("Checking database connectivity...")
            # Try to access an endpoint that requires database
            response = await self.client.get(f"{BASE_URL}/api/v1/health/")
            if response.status_code == 200:
                print("Database connectivity: PASS")
                return True
            else:
                print(f"Database connectivity: FAIL (Status: {response.status_code})")
                return False
        except Exception as e:
            print(f"Database connectivity: FAIL (Error: {e})")
            return False
    
    async def test_authentication(self) -> bool:
        """Test authentication system"""
        try:
            print("Testing authentication...")
            response = await self.client.post(
                f"{BASE_URL}/api/v1/auth/login",
                data=TEST_USER,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                print("Authentication test: PASS")
                return True
            else:
                print(f"Authentication test: FAIL (Status: {response.status_code})")
                return False
        except Exception as e:
            print(f"Authentication test: FAIL (Error: {e})")
            return False
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authorization headers"""
        if self.auth_token:
            return {"Authorization": f"Bearer {self.auth_token}"}
        return {}
    
    async def test_ai_agents(self) -> bool:
        """Test AI agents functionality"""
        try:
            print("Testing AI agents...")
            # List agents
            response = await self.client.get(
                f"{BASE_URL}/api/v1/agents/",
                headers=self.get_auth_headers()
            )
            if response.status_code != 200:
                print(f"AI agents test: FAIL (List agents failed: {response.status_code})")
                return False
            
            agents = response.json()
            print(f"Found {len(agents)} agents")
            
            # Test single agent execution (strategic agent)
            response = await self.client.post(
                f"{BASE_URL}/api/v1/agents/execute?agent_id=strategic",
                json={"description": "Quick health check test"},
                headers=self.get_auth_headers()
            )
            if response.status_code == 200:
                print("AI agents test: PASS")
                return True
            else:
                print(f"AI agents test: FAIL (Execute failed: {response.status_code})")
                return False
        except Exception as e:
            print(f"AI agents test: FAIL (Error: {e})")
            return False
    
    async def test_livekit_integration(self) -> bool:
        """Test LiveKit integration"""
        try:
            print("Testing LiveKit integration...")
            response = await self.client.get(
                f"{BASE_URL}/api/v1/livekit/health",
                headers=self.get_auth_headers()
            )
            if response.status_code == 200:
                print("LiveKit integration: PASS")
                return True
            else:
                print(f"LiveKit integration: FAIL (Status: {response.status_code})")
                return False
        except Exception as e:
            print(f"LiveKit integration: FAIL (Error: {e})")
            return False
    
    async def test_topview_integration(self) -> bool:
        """Test TopView.ai integration"""
        try:
            print("Testing TopView.ai integration...")
            response = await self.client.get(
                f"{BASE_URL}/api/v1/topview/health",
                headers=self.get_auth_headers()
            )
            if response.status_code == 200:
                print("TopView.ai integration: PASS")
                return True
            else:
                print(f"TopView.ai integration: FAIL (Status: {response.status_code})")
                return False
        except Exception as e:
            print(f"TopView.ai integration: FAIL (Error: {e})")
            return False
    
    async def test_comprehensive_health_check(self) -> bool:
        """Test the comprehensive health check endpoint"""
        try:
            print("Running comprehensive health check...")
            response = await self.client.post(
                f"{BASE_URL}/api/v1/health/check",
                json={"test_idea": "Health Check Test App"},
                headers=self.get_auth_headers()
            )
            if response.status_code == 200:
                data = response.json()
                print(f"Comprehensive health check: PASS ({len(data.get('results', []))} components checked)")
                return True
            else:
                print(f"Comprehensive health check: FAIL (Status: {response.status_code})")
                return False
        except Exception as e:
            print(f"Comprehensive health check: FAIL (Error: {e})")
            return False
    
    async def run_all_checks(self) -> bool:
        """Run all system health checks"""
        print("Starting Comprehensive System Health Check")
        print("=" * 60)
        
        checks = [
            ("Backend Health", self.check_backend_health),
            ("Database Connectivity", self.check_database_connectivity),
            ("Authentication System", self.test_authentication),
            ("AI Agents", self.test_ai_agents),
            ("LiveKit Integration", self.test_livekit_integration),
            ("TopView.ai Integration", self.test_topview_integration),
            ("Comprehensive Health Check", self.test_comprehensive_health_check),
        ]
        
        passed = 0
        total = len(checks)
        
        for check_name, check_func in checks:
            print(f"\n--- {check_name} ---")
            try:
                if await check_func():
                    passed += 1
                    self.test_results.append({"check": check_name, "status": "PASS"})
                else:
                    self.test_results.append({"check": check_name, "status": "FAIL"})
            except Exception as e:
                print(f"{check_name}: ERROR ({e})")
                self.test_results.append({"check": check_name, "status": "ERROR", "error": str(e)})
        
        # Summary
        print("\n" + "=" * 60)
        print("SYSTEM HEALTH CHECK SUMMARY")
        print("=" * 60)
        
        for result in self.test_results:
            status_icon = "✓" if result["status"] == "PASS" else "✗"
            print(f"{status_icon} {result['check']}: {result['status']}")
            if "error" in result:
                print(f"   Error: {result['error']}")
        
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        print(f"\nResults: {passed}/{total} checks passed ({(passed/total)*100:.1f}%)")
        print(f"Duration: {duration:.2f} seconds")
        
        if passed == total:
            print("\nSYSTEM HEALTH: ALL CHECKS PASSED!")
            print("Ultimate Co-founder platform is ready for use!")
            return True
        else:
            print(f"\nSYSTEM HEALTH: {total - passed} CHECKS FAILED!")
            print("Please review the failed checks above.")
            return False
    
    async def close(self):
        """Clean up resources"""
        await self.client.aclose()

async def main():
    """Main health check runner"""
    checker = SystemHealthChecker()
    try:
        success = await checker.run_all_checks()
        return 0 if success else 1
    finally:
        await checker.close()

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
