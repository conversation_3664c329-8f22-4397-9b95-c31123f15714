"""
AI Video Creation Service

This service provides integration with AI-powered video creation APIs.
Supports URL-to-Video, Video Avatar, Product Avatar, Materials-to-Video, and Image-to-Video.
"""

import os
import json
import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
from enum import Enum

logger = logging.getLogger(__name__)

class VideoTaskStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    ERROR = "error"

class VideoTaskType(Enum):
    URL_TO_VIDEO = "url_to_video"
    VIDEO_AVATAR = "video_avatar"
    PRODUCT_AVATAR = "product_avatar"
    MATERIALS_TO_VIDEO = "materials_to_video"
    PRODUCT_ANYSHOOT = "product_anyshoot"
    IMAGE_TO_VIDEO = "image_to_video"

class VideoCreationService:
    """AI Video Creation API integration service"""
    
    def __init__(self):
        self.base_url = "https://api.video-creation.ai"
        self.api_key = os.getenv("VIDEO_API_KEY", "demo_key_for_development")
        self.uid = os.getenv("VIDEO_UID", "demo_uid")
        self.session = None
        self.tasks = {}  # In-memory task storage for development
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session with proper headers"""
        if not self.session:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Video-Uid": self.uid,
                "Content-Type": "application/json"
            }
            self.session = aiohttp.ClientSession(headers=headers)
        return self.session
    
    async def close(self):
        """Close the aiohttp session"""
        if self.session:
            await self.session.close()
            self.session = None
    
    def _is_mock_mode(self) -> bool:
        """Check if running in mock mode (development)"""
        return self.api_key == "demo_key_for_development"
    
    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """Make HTTP request to TopView.ai API"""
        if self._is_mock_mode():
            return await self._mock_request(method, endpoint, data)
        
        session = await self._get_session()
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with session.request(method, url, json=data) as response:
                result = await response.json()
                
                if response.status != 200:
                    logger.error(f"TopView API error: {response.status} - {result}")
                    raise Exception(f"TopView API error: {result.get('message', 'Unknown error')}")
                
                return result
        except Exception as e:
            logger.error(f"Video API request failed: {e}")
            raise
    
    async def _mock_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """Mock API responses for development"""
        await asyncio.sleep(0.5)  # Simulate API delay
        
        task_id = f"mock_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if "submit" in endpoint:
            # Mock task submission
            mock_task = {
                "code": "200",
                "message": "Success",
                "result": {
                    "taskId": task_id,
                    "status": "processing"
                }
            }
            
            # Store mock task for later query
            self.tasks[task_id] = {
                "taskId": task_id,
                "status": "processing",
                "created_at": datetime.now(),
                "endpoint": endpoint,
                "data": data
            }
            
            # Simulate completion after 3 seconds
            asyncio.create_task(self._complete_mock_task(task_id))
            
            return mock_task
        
        elif "query" in endpoint and data and "taskId" in data:
            # Mock task query
            task_id = data["taskId"]
            task = self.tasks.get(task_id, {})
            
            return {
                "code": "200",
                "message": "Success",
                "result": {
                    "taskId": task_id,
                    "status": task.get("status", "success"),
                    "videoUrl": f"https://mock-topview-videos.s3.amazonaws.com/{task_id}.mp4",
                    "coverUrl": f"https://mock-topview-videos.s3.amazonaws.com/{task_id}_cover.jpg",
                    "videoDuration": 30000,
                    "previewVideos": [{
                        "scriptId": 0,
                        "status": "success",
                        "videoUrl": f"https://mock-topview-videos.s3.amazonaws.com/{task_id}.mp4",
                        "coverUrl": f"https://mock-topview-videos.s3.amazonaws.com/{task_id}_cover.jpg",
                        "videoDuration": 30000
                    }]
                }
            }
        
        else:
            # Mock other endpoints
            return {
                "code": "200",
                "message": "Success",
                "result": {"mock": True, "endpoint": endpoint}
            }
    
    async def _complete_mock_task(self, task_id: str):
        """Complete a mock task after delay"""
        await asyncio.sleep(3)
        if task_id in self.tasks:
            self.tasks[task_id]["status"] = "success"
    
    # URL to Video API
    async def create_url_to_video(self, url: str, options: Optional[Dict] = None) -> Dict:
        """Create video from URL using AI Video Creation API"""
        logger.info(f"Creating URL-to-Video for: {url}")
        
        data = {
            "url": url,
            "videoType": "marketing",
            "duration": options.get("duration", 30) if options else 30,
            "aspectRatio": options.get("aspectRatio", "16:9") if options else "16:9",
            "voiceId": options.get("voiceId", "default") if options else "default",
            "avatarId": options.get("avatarId", "default") if options else "default"
        }
        
        if options:
            data.update(options)
        
        return await self._make_request("POST", "/api/v1/avatar-marketing-video/submit", data)
    
    # Video Avatar API
    async def create_video_avatar(self, script: str, options: Optional[Dict] = None) -> Dict:
        """Create video with AI avatar using AI Video Creation API"""
        logger.info(f"Creating Video Avatar with script length: {len(script)}")
        
        data = {
            "script": script,
            "avatarId": options.get("avatarId", "default") if options else "default",
            "voiceId": options.get("voiceId", "default") if options else "default",
            "backgroundType": options.get("backgroundType", "solid") if options else "solid",
            "backgroundColor": options.get("backgroundColor", "#ffffff") if options else "#ffffff"
        }
        
        if options:
            data.update(options)
        
        return await self._make_request("POST", "/api/v1/video-avatar/submit", data)
    
    # Product Avatar API
    async def create_product_avatar(self, product_image_url: str, options: Optional[Dict] = None) -> Dict:
        """Create product avatar video using AI Video Creation API"""
        logger.info(f"Creating Product Avatar for image: {product_image_url}")
        
        data = {
            "productImageUrl": product_image_url,
            "avatarCategory": options.get("avatarCategory", "general") if options else "general",
            "ethnicity": options.get("ethnicity", "mixed") if options else "mixed",
            "gender": options.get("gender", "mixed") if options else "mixed"
        }
        
        if options:
            data.update(options)
        
        return await self._make_request("POST", "/api/v1/product-avatar/replace-product-image/submit", data)
    
    # Materials to Video API
    async def create_materials_to_video(self, materials: List[str], script: str, options: Optional[Dict] = None) -> Dict:
        """Create video from materials using AI Video Creation API"""
        logger.info(f"Creating Materials-to-Video with {len(materials)} materials")
        
        data = {
            "materials": materials,
            "script": script,
            "videoType": options.get("videoType", "marketing") if options else "marketing",
            "duration": options.get("duration", 30) if options else 30,
            "voiceId": options.get("voiceId", "default") if options else "default"
        }
        
        if options:
            data.update(options)
        
        return await self._make_request("POST", "/api/v1/avatar-marketing-video/submit", data)
    
    # Image to Video API
    async def create_image_to_video(self, image_url: str, options: Optional[Dict] = None) -> Dict:
        """Create video from image using AI Video Creation API"""
        logger.info(f"Creating Image-to-Video for: {image_url}")
        
        data = {
            "imageUrl": image_url,
            "duration": options.get("duration", 5) if options else 5,
            "motionStrength": options.get("motionStrength", "medium") if options else "medium"
        }
        
        if options:
            data.update(options)
        
        return await self._make_request("POST", "/api/v1/common-task/image2video/submit", data)
    
    # Task Query API
    async def query_task(self, task_id: str) -> Dict:
        """Query task status using AI Video Creation API"""
        logger.info(f"Querying task: {task_id}")
        
        data = {"taskId": task_id}
        
        # Determine the correct query endpoint based on task type
        # For now, use the general avatar marketing video query
        return await self._make_request("GET", f"/api/v1/avatar-marketing-video/query?taskId={task_id}")
    
    # Utility methods
    async def get_available_voices(self) -> List[Dict]:
        """Get list of available voices"""
        if self._is_mock_mode():
            return [
                {"id": "default", "name": "Default Voice", "language": "en", "gender": "female"},
                {"id": "male_1", "name": "Professional Male", "language": "en", "gender": "male"},
                {"id": "female_1", "name": "Friendly Female", "language": "en", "gender": "female"},
                {"id": "narrator", "name": "Documentary Narrator", "language": "en", "gender": "male"}
            ]
        
        result = await self._make_request("GET", "/api/v1/voice/query")
        return result.get("result", [])
    
    async def get_available_avatars(self) -> List[Dict]:
        """Get list of available AI avatars"""
        if self._is_mock_mode():
            return [
                {"id": "default", "name": "Professional Woman", "ethnicity": "mixed", "gender": "female"},
                {"id": "male_1", "name": "Business Man", "ethnicity": "caucasian", "gender": "male"},
                {"id": "female_2", "name": "Tech Presenter", "ethnicity": "asian", "gender": "female"},
                {"id": "narrator", "name": "Documentary Host", "ethnicity": "mixed", "gender": "male"}
            ]
        
        result = await self._make_request("GET", "/api/v1/ai-avatar/query")
        return result.get("result", [])

# Global service instance
video_creation_service = VideoCreationService()
