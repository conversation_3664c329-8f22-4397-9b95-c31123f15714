from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Boolean
from sqlalchemy.sql import func
from app.core.database import Base

class Session(Base):
    __tablename__ = "sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(100), unique=True, index=True)
    session_type = Column(String(20))  # voice, video, screen-share
    status = Column(String(20), default="pending")  # pending, active, completed, failed
    room_name = Column(String(100))
    livekit_token = Column(Text)
    participants = Column(JSON)  # List of participant info
    duration = Column(Integer, default=0)  # Duration in seconds
    recording_url = Column(String(500))
    transcript = Column(JSON)  # List of messages
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))