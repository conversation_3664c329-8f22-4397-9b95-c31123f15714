from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
from pydantic import BaseModel
from datetime import datetime

from app.services.livekit_service import livekit_service

router = APIRouter()

class CreateSessionRequest(BaseModel):
    agent_ids: List[str]
    session_type: str = "video"  # voice, video, screen-share

class SendMessageRequest(BaseModel):
    participant_id: str
    message: str

@router.post("/sessions")
async def create_session(request: CreateSessionRequest):
    """Create a new LiveKit session"""
    try:
        session = await livekit_service.create_session(
            agent_ids=request.agent_ids,
            session_type=request.session_type
        )
        return session
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating session: {str(e)}")

@router.get("/sessions/{session_id}")
async def get_session(session_id: str):
    """Get session details"""
    session = await livekit_service.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    return session

@router.post("/sessions/{session_id}/start")
async def start_session(session_id: str):
    """Start a LiveKit session"""
    try:
        await livekit_service.update_session_status(session_id, "active")
        session = await livekit_service.get_session(session_id)
        return {"message": "Session started", "session": session}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting session: {str(e)}")

@router.post("/sessions/{session_id}/end")
async def end_session(session_id: str):
    """End a LiveKit session"""
    try:
        await livekit_service.end_session(session_id)
        session = await livekit_service.get_session(session_id)
        return {"message": "Session ended", "session": session}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error ending session: {str(e)}")

@router.post("/sessions/{session_id}/messages")
async def send_message(session_id: str, request: SendMessageRequest):
    """Send message in session"""
    try:
        message = await livekit_service.send_message(
            session_id=session_id,
            participant_id=request.participant_id,
            message=request.message
        )
        return message
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error sending message: {str(e)}")

@router.get("/sessions/{session_id}/messages")
async def get_session_messages(session_id: str):
    """Get session messages"""
    session = await livekit_service.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return {"messages": session.get("messages", [])}

@router.post("/sessions/{session_id}/participants")
async def add_participant(session_id: str, participant_id: str, participant_name: str):
    """Add participant to session"""
    try:
        await livekit_service.add_participant(session_id, participant_id, participant_name)
        return {"message": f"Participant {participant_name} added to session"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error adding participant: {str(e)}")

@router.get("/")
async def get_livekit_status():
    """Get LiveKit service status"""
    return {
        "status": "active",
        "active_sessions": len(livekit_service.sessions),
        "sessions": list(livekit_service.sessions.keys())
    }

@router.get("/health")
async def get_livekit_health():
    """Get LiveKit service health status"""
    try:
        active_sessions = len(livekit_service.sessions)
        return {
            "status": "healthy",
            "active_sessions": active_sessions,
            "service": "LiveKit",
            "timestamp": datetime.now().isoformat(),
            "checks": {
                "service_running": True,
                "sessions_accessible": True,
                "configuration_valid": True
            }
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }