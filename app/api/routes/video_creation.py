"""
TopView.ai API Routes

Provides REST API endpoints for TopView.ai video creation functionality.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from app.services.video_creation_service import video_creation_service, VideoTaskType
from app.api.routes.auth import get_current_user
from app.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(tags=["topview"])

# Request Models
class URLToVideoRequest(BaseModel):
    url: str = Field(..., description="URL to convert to video")
    duration: Optional[int] = Field(30, description="Video duration in seconds")
    aspectRatio: Optional[str] = Field("16:9", description="Video aspect ratio")
    voiceId: Optional[str] = Field("default", description="Voice ID for narration")
    avatarId: Optional[str] = Field("default", description="Avatar ID for presentation")
    title: Optional[str] = Field(None, description="Custom video title")
    description: Optional[str] = Field(None, description="Custom video description")

class VideoAvatarRequest(BaseModel):
    script: str = Field(..., description="Script for the avatar to speak")
    avatarId: Optional[str] = Field("default", description="Avatar ID")
    voiceId: Optional[str] = Field("default", description="Voice ID")
    backgroundType: Optional[str] = Field("solid", description="Background type")
    backgroundColor: Optional[str] = Field("#ffffff", description="Background color")
    duration: Optional[int] = Field(None, description="Maximum video duration")

class ProductAvatarRequest(BaseModel):
    productImageUrl: str = Field(..., description="Product image URL")
    avatarCategory: Optional[str] = Field("general", description="Avatar category")
    ethnicity: Optional[str] = Field("mixed", description="Avatar ethnicity")
    gender: Optional[str] = Field("mixed", description="Avatar gender")
    script: Optional[str] = Field(None, description="Product presentation script")

class MaterialsToVideoRequest(BaseModel):
    materials: List[str] = Field(..., description="List of material URLs (images, videos)")
    script: str = Field(..., description="Video script")
    videoType: Optional[str] = Field("marketing", description="Type of video")
    duration: Optional[int] = Field(30, description="Video duration in seconds")
    voiceId: Optional[str] = Field("default", description="Voice ID")
    title: Optional[str] = Field(None, description="Video title")

class ImageToVideoRequest(BaseModel):
    imageUrl: str = Field(..., description="Image URL to animate")
    duration: Optional[int] = Field(5, description="Video duration in seconds")
    motionStrength: Optional[str] = Field("medium", description="Motion strength: low, medium, high")

class TaskQueryRequest(BaseModel):
    taskId: str = Field(..., description="Task ID to query")

# Response Models
class TaskResponse(BaseModel):
    taskId: str
    status: str
    message: str
    videoType: str
    createdAt: datetime

class VideoResult(BaseModel):
    taskId: str
    status: str
    videoUrl: Optional[str] = None
    coverUrl: Optional[str] = None
    videoDuration: Optional[int] = None
    previewVideos: Optional[List[Dict]] = None
    errorMsg: Optional[str] = None

# API Endpoints

@router.post("/url-to-video", response_model=TaskResponse)
async def create_url_to_video(
    request: URLToVideoRequest,
    current_user: User = Depends(get_current_user)
):
    """Create video from URL using TopView.ai URL-to-Video API"""
    try:
        logger.info(f"User {current_user.email} creating URL-to-Video for: {request.url}")
        
        options = {
            "duration": request.duration,
            "aspectRatio": request.aspectRatio,
            "voiceId": request.voiceId,
            "avatarId": request.avatarId
        }
        
        if request.title:
            options["title"] = request.title
        if request.description:
            options["description"] = request.description
        
        result = await video_creation_service.create_url_to_video(request.url, options)
        
        if result.get("code") != "200":
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to create video"))
        
        task_data = result.get("result", {})
        
        return TaskResponse(
            taskId=task_data.get("taskId"),
            status=task_data.get("status", "pending"),
            message="URL-to-Video task created successfully",
            videoType=VideoTaskType.URL_TO_VIDEO.value,
            createdAt=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"URL-to-Video creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create URL-to-Video: {str(e)}")

@router.post("/video-avatar", response_model=TaskResponse)
async def create_video_avatar(
    request: VideoAvatarRequest,
    current_user: User = Depends(get_current_user)
):
    """Create video with AI avatar using TopView.ai Video Avatar API"""
    try:
        logger.info(f"User {current_user.email} creating Video Avatar with script length: {len(request.script)}")
        
        options = {
            "avatarId": request.avatarId,
            "voiceId": request.voiceId,
            "backgroundType": request.backgroundType,
            "backgroundColor": request.backgroundColor
        }
        
        if request.duration:
            options["duration"] = request.duration
        
        result = await video_creation_service.create_video_avatar(request.script, options)
        
        if result.get("code") != "200":
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to create video avatar"))
        
        task_data = result.get("result", {})
        
        return TaskResponse(
            taskId=task_data.get("taskId"),
            status=task_data.get("status", "pending"),
            message="Video Avatar task created successfully",
            videoType=VideoTaskType.VIDEO_AVATAR.value,
            createdAt=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Video Avatar creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create Video Avatar: {str(e)}")

@router.post("/product-avatar", response_model=TaskResponse)
async def create_product_avatar(
    request: ProductAvatarRequest,
    current_user: User = Depends(get_current_user)
):
    """Create product avatar video using TopView.ai Product Avatar API"""
    try:
        logger.info(f"User {current_user.email} creating Product Avatar for: {request.productImageUrl}")
        
        options = {
            "avatarCategory": request.avatarCategory,
            "ethnicity": request.ethnicity,
            "gender": request.gender
        }
        
        if request.script:
            options["script"] = request.script
        
        result = await video_creation_service.create_product_avatar(request.productImageUrl, options)
        
        if result.get("code") != "200":
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to create product avatar"))
        
        task_data = result.get("result", {})
        
        return TaskResponse(
            taskId=task_data.get("taskId"),
            status=task_data.get("status", "pending"),
            message="Product Avatar task created successfully",
            videoType=VideoTaskType.PRODUCT_AVATAR.value,
            createdAt=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Product Avatar creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create Product Avatar: {str(e)}")

@router.post("/materials-to-video", response_model=TaskResponse)
async def create_materials_to_video(
    request: MaterialsToVideoRequest,
    current_user: User = Depends(get_current_user)
):
    """Create video from materials using TopView.ai Materials-to-Video API"""
    try:
        logger.info(f"User {current_user.email} creating Materials-to-Video with {len(request.materials)} materials")
        
        options = {
            "videoType": request.videoType,
            "duration": request.duration,
            "voiceId": request.voiceId
        }
        
        if request.title:
            options["title"] = request.title
        
        result = await video_creation_service.create_materials_to_video(request.materials, request.script, options)
        
        if result.get("code") != "200":
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to create materials video"))
        
        task_data = result.get("result", {})
        
        return TaskResponse(
            taskId=task_data.get("taskId"),
            status=task_data.get("status", "pending"),
            message="Materials-to-Video task created successfully",
            videoType=VideoTaskType.MATERIALS_TO_VIDEO.value,
            createdAt=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Materials-to-Video creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create Materials-to-Video: {str(e)}")

@router.post("/image-to-video", response_model=TaskResponse)
async def create_image_to_video(
    request: ImageToVideoRequest,
    current_user: User = Depends(get_current_user)
):
    """Create video from image using TopView.ai Image2Video API"""
    try:
        logger.info(f"User {current_user.email} creating Image-to-Video for: {request.imageUrl}")
        
        options = {
            "duration": request.duration,
            "motionStrength": request.motionStrength
        }
        
        result = await video_creation_service.create_image_to_video(request.imageUrl, options)
        
        if result.get("code") != "200":
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to create image video"))
        
        task_data = result.get("result", {})
        
        return TaskResponse(
            taskId=task_data.get("taskId"),
            status=task_data.get("status", "pending"),
            message="Image-to-Video task created successfully",
            videoType=VideoTaskType.IMAGE_TO_VIDEO.value,
            createdAt=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Image-to-Video creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create Image-to-Video: {str(e)}")

@router.get("/task/{task_id}", response_model=VideoResult)
async def query_task(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """Query task status and get video results"""
    try:
        logger.info(f"User {current_user.email} querying task: {task_id}")
        
        result = await video_creation_service.query_task(task_id)
        
        if result.get("code") != "200":
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to query task"))
        
        task_data = result.get("result", {})
        
        return VideoResult(
            taskId=task_data.get("taskId", task_id),
            status=task_data.get("status", "unknown"),
            videoUrl=task_data.get("videoUrl"),
            coverUrl=task_data.get("coverUrl"),
            videoDuration=task_data.get("videoDuration"),
            previewVideos=task_data.get("previewVideos"),
            errorMsg=task_data.get("errorMsg")
        )
        
    except Exception as e:
        logger.error(f"Task query failed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to query task: {str(e)}")

@router.get("/voices")
async def get_available_voices(current_user: User = Depends(get_current_user)):
    """Get list of available voices for video creation"""
    try:
        voices = await video_creation_service.get_available_voices()
        return {"voices": voices}
    except Exception as e:
        logger.error(f"Failed to get voices: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get voices: {str(e)}")

@router.get("/avatars")
async def get_available_avatars(current_user: User = Depends(get_current_user)):
    """Get list of available AI avatars for video creation"""
    try:
        avatars = await video_creation_service.get_available_avatars()
        return {"avatars": avatars}
    except Exception as e:
        logger.error(f"Failed to get avatars: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get avatars: {str(e)}")

@router.get("/health")
async def get_video_creation_health():
    """Get AI Video Creation service health status"""
    try:
        return {
            "status": "healthy",
            "service": "AI Video Creation",
            "timestamp": datetime.now().isoformat(),
            "mock_mode": video_creation_service._is_mock_mode(),
            "api_key_configured": bool(video_creation_service.api_key and video_creation_service.api_key != "demo_key_for_development"),
            "features": [
                "URL-to-Video",
                "Video Avatar",
                "Product Avatar",
                "Materials-to-Video",
                "Image-to-Video"
            ]
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")
